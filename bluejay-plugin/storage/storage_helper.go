package storage

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/heroiclabs/nakama-common/runtime"
)

const (
	ENTITLEMENT_COLLECTION            string = "entitlements"
	ENTITLEMENTS                      string = "item_entitlements"
	EQUIPPED_LOADOUT                  string = "equipped_loadout"
	LOADOUT_COLLECTION                string = "loadout"
	MATCHMAKING_COLLECTION            string = "matchmaking"
	PROGRESSION_CHALLENGES            string = "challenges"
	ACTIVE_CHALLENGES                 string = "active"
	CHALLENGE_HISTORY                 string = "history"
	PROGRESSION_CHALLENGES_COLLECTION string = "challenges"
	PROGRESSION_MATCH_COLLECTION      string = "matches"
	PROGRESSION_STATS                 string = "stats"
	PROGRESSION_STATS_COLLECTION      string = "stats"
	TROVE_CURRENCY_COLLECTION         string = "trove_currency"
	TROVE_CURRENCY                    string = "trove_currency"
	USER_LAST_MATCH                   string = "last_match"
	USER_PREVIOUS_MATCH               string = "previous_match"
	USER_SKILL                        string = "skill"
	USER_STANDING                     string = "standing"
)

func ReadPlayerStorageObjects[T any](ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, environment string, collection string, key string, playerIds []string, defaultResult func() T) (map[string]T, error) {

	logger = logger.WithField("collection", collection).WithField("key", key)

	readRequest := make([]*runtime.StorageRead, len(playerIds))

	result := map[string]T{}
	for i, playerId := range playerIds {
		readRequest[i] = &runtime.StorageRead{
			Collection: collection,
			Key:        key + "." + environment,
			UserID:     playerId,
		}
	}

	readResult, err := nk.StorageRead(ctx, readRequest)
	if err != nil {
		logger.WithField("err", err).Error("error reading from storage")
		return nil, err
	} else {
		for _, record := range readResult {
			userId := record.GetUserId()

			var resultObject T
			err = json.Unmarshal([]byte(record.Value), &resultObject)
			if err != nil {
				logger.WithField("err", err).WithField("userId", userId).WithField("json", record.Value).Error("error unmarshalling standings")
			} else {
				result[userId] = resultObject
			}
		}
	}

	for _, playerId := range playerIds {
		_, found := result[playerId]
		if !found {
			result[playerId] = defaultResult()
		}
	}

	return result, nil
}

func UpdatePlayerStorageObjects[T any](ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, environment string, collection string, key string, newRecords map[string]T) error {
	updateRequest := make([]*runtime.StorageWrite, len(newRecords))

	i := 0
	for playerId, newRecord := range newRecords {
		jsonRecord, err := json.Marshal(newRecord)
		if err != nil {
			return err
		}

		updateRequest[i] = &runtime.StorageWrite{
			Collection: collection,
			Key:        key + "." + environment,
			UserID:     playerId,
			Value:      string(jsonRecord),
		}
		i++
	}

	result, err := nk.StorageWrite(ctx, updateRequest)
	if err != nil {
		return err
	}

	logger.WithField("ack", result).WithField("collection", collection).WithField("key", key).Info(fmt.Sprintf("Updated storage records for %d players", len(newRecords)))

	return nil
}

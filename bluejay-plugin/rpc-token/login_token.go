package token

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/configs"
)

func GetLoginToken(
	ctx context.Context,
	logger runtime.Logger,
	db *sql.DB,
	nk runtime.NakamaModule,
	payload string) (string, error) {

	logger.Debug("RPC Get Login Token")

	callerID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

	cfg, err := configs.ReturnVivoxConfig()
	if err != nil {
		return "", fmt.Errorf("invalid Vivox config file: %w", err)
	}

	ctg, err := configs.ReturnTokenConfig()
	if err != nil {
		return "", fmt.Errorf("invalid token config file: %w", err)
	}

	authToken, err := GenerateToken(
		ctg.VXATypes.VXALogin,
		ctg.TokenExpire.GeneralTTL,
		callerID,
		"",
		"",
		"",
		logger,
		&cfg,
		&ctg,
	)

	if err != nil {
		return "", err
	}

	return authToken, nil
}

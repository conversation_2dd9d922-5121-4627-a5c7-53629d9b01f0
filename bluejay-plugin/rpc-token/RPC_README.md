
### Register RPCs


- `Get<PERSON><PERSON><PERSON>Token` - Generate token to sign in a user to the server.
- `GetJoinToken` - Generate token to join a player into a channel. ( Not muted )
- `GetJoinMutedToken` - Generate token to join a player into a channel. ( Muted )
- `GetTranscriptionToken` - Generate token that allows the user to begin a speech-to-text transcription for the channel

- `SetBlockPlayer` - Calls the Nakama to block a user
- `SetUnblockPlayer` - Calls the Nakama to unblock a user
- `GetListBlockedPlayers` - Returns a list of players "Blocked"

## Payload Request Struct

Below are all the RPCs payload structures keeping in mind that every single one requires 
player ID. For easy testing, use nakama [API dashboard](http://127.0.0.1:7351/#/apiexplorer).


### Get Login Token
Only requires the client to be passed.

### Get Join Token // Get Join Token Muted


Condition: If the party id is provided empty, an error 
will inform the player that they must be at a party to retrieve this token.
```
{
    "ChannelID": "<string>",
    "ChannelType":"<string>" // Must always be between `echo` / `positional` / `non_positional`
} 
```

Example:

Header: 00000000-0000-0000-0000-000000000000 <br>
RequestBody:
```
{
    "ChannelID": "ChannelNameHere",
    "ChannelType":"positional"
} 
```

---

### Get Transcription Token
```
{
    "ChannelID":"<string>",
    "ChannelType":"<string>"
} 
```

Example:

Header: 00000000-0000-0000-0000-000000000000
RequestBody:
```
{
    "ChannelID": "ChannelNameHere",
    "ChannelType":"positional"
} 
```

----

### Set Block Player
```
{
    "BlockUserID":"<string>"
} 
```

Example:

Header: 00000000-0000-0000-0000-000000000000
RequestBody:
```
{
     "BlockUserID":"00000000-0000-0000-0000-0123123131231"
} 
```
----

### Set Unblock Player
```
{
    "UnblockUserID":"<string>"
} 
```

Example:

Header: 00000000-0000-0000-0000-000000000000
RequestBody:
```
{
     "UnblockUserID":"00000000-0000-0000-0000-0123123131231"
} 
```

----

### RPC_ListBlockedPlayers
Will return all the players that the current user has blocked.

Example:

Header: 00000000-0000-0000-0000-000000000000

----



## Payload Answers Struct

All the payload answers from these rpcs except the Block and ListBlock, will return
```
{
    "access_token":"VIVOXTokenHere",
    "expires_in": "ExpireTimeForToken"
} 
```

`SetBlockPlayer` 
```
{
  "success_block": "True"
}
```

`SetUnblockPlayer`
```
{
  "success_unblock": "True"
}
```

`GetListBlockedPlayers` will return an array

```
[
    {
        "id": "8ca50de2-e59f-44fb-a400-ffa1f1181a53",
        "username": "gJlgnaTzxE"
    },
    {
        "id": "a7f8cf23-15a3-4ff4-9c7c-e1795004afff",
        "username": "Test2Nakama"
    }
]
```

## Config Files

VIVOXConfix.json -> Contains all the credentials for VIVOX

TokenConfig.json -> Contains all the options that the token can contain

## Other Files

### GenerateToken.go 
Internal function that generates the token for all the RPCs based on the conditions passed
package token

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/errors"
)

type SuccessBlockResponse struct {
	Success string `json:"success_block"`
}

func SetBlockPlayer(
	ctx context.Context,
	logger runtime.Logger,
	db *sql.DB,
	nk runtime.NakamaModule,
	payload string) (string, error) {

	callerID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	}

	request := &BlockPlayerPayloadParams{}
	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
		return "", errors.InvalidPayload
	}

	if request.BlockUserID == "" {
		logger.Error("Invalid block player ID cannot be empty")
		return "", errors.NoTargetIdFound
	}

	accountToBlockUsername, err := GetAccountUsername(request.BlockUserID, ctx, logger, nk)
	if err != nil {
		logger.WithField("err", err).Error("Invalid user ID")
		return "", errors.AccountNotFound
	}

	accountRequestingToBlockUsername, err := GetAccountUsername(callerID, ctx, logger, nk)
	if err != nil {
		logger.WithField("err", err).Error("Invalid requesting user ID")
		return "", errors.AccountNotFound
	}

	ids := []string{request.BlockUserID}
	usernames := []string{accountToBlockUsername}
	if err := nk.FriendsBlock(ctx, callerID, accountRequestingToBlockUsername, ids, usernames); err != nil {
		logger.WithField("err", err).Error("Failed to block user")
		return "", errors.RPCFailedAction
	}

	response := SuccessBlockResponse{Success: "True"}
	resBytes, err := json.Marshal(response)
	if err != nil {
		logger.WithField("err", err).Error("Failed to marshal success response")
		return "", errors.RPCFailedAction
	}

	return string(resBytes), nil
}

func GetAccountUsername(userID string, ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule) (string, error) {

	account, err := nk.AccountGetId(ctx, userID)
	if err != nil {
		logger.WithField("err", err).Error("Account not found")
		return "", errors.AccountNotFound
	}

	return account.GetUser().Username, nil
}

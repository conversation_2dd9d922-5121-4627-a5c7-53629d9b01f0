package token

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/configs"
	"wildlight.gg/bluejay/errors"
)

func GetJoinToken(
	ctx context.Context,
	logger runtime.Logger,
	db *sql.DB,
	nk runtime.NakamaModule,
	payload string) (string, error) {

	callerID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	}

	request := &JoinTokenPayloadParams{}
	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
		return "", errors.InvalidPayload
	}

	if request.ChannelID == "" {
		logger.Error("Player currently does not have a party")
		return "", errors.PartyNotFound
	}

	cfg, err := configs.ReturnVivoxConfig()
	if err != nil {
		return "", errors.MissingVivoxConfig
	}

	ctg, err := configs.ReturnTokenConfig()
	if err != nil {
		return "", errors.MissingTokenConfig
	}

	authToken, err := GenerateToken(
		ctg.VXATypes.VXAJoin,
		ctg.TokenExpire.GeneralTTL,
		callerID,
		"",
		request.ChannelID,
		request.ChannelType,
		logger,
		&cfg,
		&ctg,
	)

	return authToken, nil
}

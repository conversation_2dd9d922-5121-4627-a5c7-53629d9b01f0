package token

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/heroiclabs/nakama-common/runtime"
	"sync/atomic"
	"time"
	"wildlight.gg/bluejay/configs"
	"wildlight.gg/bluejay/errors"
)

var seq uint64 = 0

type GenerateTokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresAt   int64  `json:"expires_in"`
}

type tokenPayload struct {
	Iss string `json:"iss"`
	Exp int64  `json:"exp"`
	Vxa string `json:"vxa"`
	Vxi string `json:"vxi"`
	F   string `json:"f"`
	T   string `json:"t,omitempty"`
	Sub string `json:"sub,omitempty"`
}

type ClaimSpec struct {
	FSource   string
	TSource   string
	SubSource string
}

var actionSpecs = map[string]ClaimSpec{
	"login":      {FSource: "user", TSource: "", SubSource: ""},
	"join":       {FSource: "user", TSource: "channel", SubSource: ""},
	"join_muted": {FSource: "user", TSource: "channel", SubSource: ""},
	"kick":       {FSource: "targetUser", TSource: "channel", SubSource: "moderator"},
	"mute":       {FSource: "targetUser", TSource: "channel", SubSource: "moderator"},
	"mute_all":   {FSource: "moderator", TSource: "channel", SubSource: ""},
	"trxn":       {FSource: "moderator", TSource: "channel", SubSource: ""},
}

func GenerateToken(
	tokenType string,
	ttl int64,
	requestUserID string,
	targetUserID string,
	channelID string,
	channelType string,
	logger runtime.Logger,
	cfg *configs.VivoxConfig,
	ctg *configs.TokenConfig) (string, error) {

	specKey := tokenType
	spec, ok := actionSpecs[specKey]
	if !ok {
		return "", errors.TokenGenerationFailed
	}

	now := time.Now().Unix()
	exp := now + ttl

	// Build JWT header
	header := map[string]string{"alg": "HS256", "typ": "JWT"}
	hb, _ := json.Marshal(header)
	headerEnc := base64.RawURLEncoding.EncodeToString(hb)

	// Generate a unique VXI
	vxi := GenerateVXI()

	// Start payload
	payload := tokenPayload{
		Iss: cfg.Credentials.Issuer,
		Exp: exp,
		Vxa: tokenType,
		Vxi: vxi,
		F:   "",
		T:   "",
		Sub: "",
	}

	// Populate F, T, Sub based on spec
	if err := populateClaimsFromSpec(&payload, spec, requestUserID, targetUserID, channelID, channelType, cfg, ctg); err != nil {
		return "", err
	}

	// 7) Marshal payload → Base64
	pb, err := json.Marshal(payload)
	if err != nil {
		return "", errors.PayloadMarshalFailed
	}
	payloadEnc := base64.RawURLEncoding.EncodeToString(pb)

	// 8) Sign with HMAC SHA256
	signingInput := fmt.Sprintf("%s.%s", headerEnc, payloadEnc)
	mac := hmac.New(sha256.New, []byte(cfg.Credentials.TokenKey))
	mac.Write([]byte(signingInput))
	rawSig := mac.Sum(nil)
	sigEnc := base64.RawURLEncoding.EncodeToString(rawSig)

	accessToken := fmt.Sprintf("%s.%s.%s", headerEnc, payloadEnc, sigEnc)

	// Wrap in JSON
	resp := GenerateTokenResponse{AccessToken: accessToken, ExpiresAt: exp}
	rb, err := json.Marshal(resp)
	if err != nil {
		logger.WithField("err", err).Error("Failed to generate token")
		return "", errors.RPCFailedAction
	}

	return string(rb), nil
}

// populateClaimsFromSpec uses the spec to set payload.F, payload.T, and payload.Sub.
func populateClaimsFromSpec(
	payload *tokenPayload,
	spec ClaimSpec,
	requestUserID, targetUserID, channelID, channelType string,
	vivoxCfg *configs.VivoxConfig,
	tokenCfg *configs.TokenConfig,
) error {
	issuer := vivoxCfg.Credentials.Issuer
	domain := vivoxCfg.Credentials.Domain

	// F
	switch spec.FSource {
	case "user":
		payload.F = buildUserURI(issuer, requestUserID, domain)
	case "targetUser":
		if targetUserID == "" {
			return errors.NoTargetIdFound
		}
		payload.F = buildUserURI(issuer, targetUserID, domain)
	case "moderator":
		payload.F = buildUserURI(issuer, requestUserID, domain)
	case "":
		// nothing
	default:
		return errors.InvalidPayload
	}

	// T (always “channel” when non-empty)
	if spec.TSource == "channel" {
		// lookup single-letter code for channelType
		code, ok := mapChannelTypeToCode(channelType, tokenCfg)
		if !ok {
			return errors.InvalidPayload
		}
		payload.T = buildChannelURI(issuer, channelID, domain, code)
	}

	//Sub (only “moderator” for kick/mute)
	if spec.SubSource == "moderator" {
		payload.Sub = buildUserURI(issuer, requestUserID, domain)
	}

	return nil
}

// buildUserURI returns "sip:.<issuer>.<userID>.@<domain>"
func buildUserURI(issuer, userID, domain string) string {
	return fmt.Sprintf("sip:.%s.%s.@%s", issuer, userID, domain)
}

// buildChannelURI returns "sip:confctl-<code>-<issuer>.<channelID>@<domain>"
func buildChannelURI(issuer, channelID, domain, code string) string {
	return fmt.Sprintf("sip:confctl-%s-%s.%s@%s", code, issuer, channelID, domain)
}

// mapChannelTypeToCode looks up the 1-letter channel code from your JSON config.
func mapChannelTypeToCode(channelType string, tokenCfg *configs.TokenConfig) (string, bool) {
	switch channelType {
	case "positional":
		return tokenCfg.ChannelType.Positional, true
	case "non_positional":
		return tokenCfg.ChannelType.NonPositional, true
	case "echo":
		return tokenCfg.ChannelType.Echo, true
	case "none":
		return tokenCfg.ChannelType.None, true
	default:
		return "", false
	}
}

func GenerateVXI() string {
	n := atomic.AddUint64(&seq, 1)
	return fmt.Sprintf("%04d", n)
}

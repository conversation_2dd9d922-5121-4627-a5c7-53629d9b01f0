package token

import (
	"context"
	"database/sql"
	"encoding/json"
	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/errors"
)

func GetListBlockedPlayers(
	ctx context.Context,
	logger runtime.Logger,
	db *sql.DB,
	nk runtime.NakamaModule,
	payload string) (string, error) {

	callerID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

	if !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	}

	blockedState := 3
	const limit = 100

	friendsSlice, _, err := nk.FriendsList(ctx, callerID, limit, &blockedState, "")
	if err != nil {
		logger.WithField("err", err).Error("Failed to list blocked users")
		return "", errors.InformationNotFound
	}

	blockedList := make([]BlockedPlayerInfo, 0)
	for _, friend := range friendsSlice {
		user := friend.GetUser()
		blockedList = append(blockedList, BlockedPlayerInfo{
			Id:       user.GetId(),
			Username: user.GetUsername(),
		})
	}

	out, err := json.Marshal(blockedList)
	if err != nil {
		logger.WithField("err", err).Error("Failed to marshal blocked list")
		return "", errors.PayloadMarshalFailed
	}

	return string(out), nil
}

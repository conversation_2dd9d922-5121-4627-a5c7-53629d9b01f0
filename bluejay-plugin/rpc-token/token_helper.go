package token

type JoinTokenPayloadParams struct {
	ChannelID   string `json:"channelID" validate:"required"`
	ChannelType string `json:"channelType" validate:"required,oneof=positional non_positional echo"`
}

type TranscriptionTokenPayloadParams struct {
	ChannelID   string `json:"channelID" validate:"required"`
	ChannelType string `json:"channelType" validate:"required,oneof=positional non_positional echo"`
}

type BlockedPlayerInfo struct {
	Id       string `json:"id"`
	<PERSON>rna<PERSON> string `json:"username"`
}

type BlockPlayerPayloadParams struct {
	BlockUserID string `json:"blockUserID"  validate:"required"`
}

type UnblockPlayerPayloadParams struct {
	UnblockUserID string `json:"unblockUserID"  validate:"required"`
}

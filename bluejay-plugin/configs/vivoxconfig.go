package configs

import (
	"encoding/json"
	"fmt"
	"os"
)

type VivoxConfig struct {
	Credentials ConfigParams `json:"vivox"`
}

type ConfigParams struct {
	Endpoint string `json:"endpoint"`
	Domain   string `json:"domain"`
	Issuer   string `json:"issuer"`
	TokenKey string `json:"token_key"`
}

func ReturnVivoxConfig() (VivoxConfig, error) {

	dir, err := os.Getwd()
	if err != nil {
		fmt.Println("Error:", err)
		return VivoxConfig{}, err
	}
	fmt.Println("Current working directory:", dir)

	data, err := os.ReadFile("config/jsondata/VIVOXConfig.json")
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error reading jsondata/VIVOXConfig.json: %v\n", err)
		return VivoxConfig{}, err
	}

	var cfg VivoxConfig
	if err := json.Unmarshal(data, &cfg); err != nil {
		fmt.Fprintf(os.Stderr, "Error parsing JSON: %v\n", err)
		return VivoxConfig{}, err
	}

	return cfg, nil
}

package configs

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
)

type TokenConfig struct {
	TokenExpire TokenExpire `json:"token_expire_time"`
	ChannelType ChannelType `json:"channel_type"`
	VXATypes    VXATypes    `json:"VXA_types"`
}

type TokenExpire struct {
	GeneralTTL int64 `json:"generalTTL"`
}

type ChannelType struct {
	Positional    string `json:"positional"`
	NonPositional string `json:"non_positional"`
	Echo          string `json:"echo"`
	None          string `json:"none"`
}

type VXATypes struct {
	VXALogin         string `json:"VXA_Login"`
	VXAJoin          string `json:"VXA_Join"`
	VXAJoinMuted     string `json:"VXA_JoinMuted"`
	VXATranscription string `json:"VXA_Transcription"`
}

func ReturnTokenConfig() (TokenConfig, error) {
	data, err := ioutil.ReadFile("config/jsondata/TokenConfig.json")
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error reading jsondata/TokenConfig.json: %v\n", err)
		return TokenConfig{}, err
	}

	var cfg TokenConfig
	if err := json.Unmarshal(data, &cfg); err != nil {
		fmt.Fprintf(os.Stderr, "Error parsing JSON: %v\n", err)
		return TokenConfig{}, err
	}

	return cfg, nil
}

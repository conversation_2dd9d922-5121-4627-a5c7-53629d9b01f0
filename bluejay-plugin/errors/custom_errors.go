package errors

import (
	"fmt"

	"github.com/heroiclabs/nakama-common/runtime"
)

const (
	// OK is returned on success.
	OK int = 0

	// Canceled indicates the operation was canceled (typically by the caller).
	//
	// The gRPC framework will generate this error code when cancellation
	// is requested.
	Canceled int = 1

	// Unknown error. An example of where this error may be returned is
	// if a Status value received from another address space belongs to
	// an error-space that is not known in this address space. Also
	// errors raised by APIs that do not return enough error information
	// may be converted to this error.
	//
	// The gRPC framework will generate this error code in the above two
	// mentioned cases.
	Unknown int = 2

	// InvalidArgument indicates client specified an invalid argument.
	// Note that this differs from FailedPrecondition. It indicates arguments
	// that are problematic regardless of the state of the system
	// (e.g., a malformed file name).
	//
	// This error code will not be generated by the gRPC framework.
	InvalidArgument int = 3

	// DeadlineExceeded means operation expired before completion.
	// For operations that change the state of the system, this error may be
	// returned even if the operation has completed successfully. For
	// example, a successful response from a server could have been delayed
	// long enough for the deadline to expire.
	//
	// The gRPC framework will generate this error code when the deadline is
	// exceeded.
	DeadlineExceeded int = 4

	// NotFound means some requested entity (e.g., file or directory) was
	// not found.
	//
	// This error code will not be generated by the gRPC framework.
	NotFound int = 5

	// AlreadyExists means an attempt to create an entity failed because one
	// already exists.
	//
	// This error code will not be generated by the gRPC framework.
	AlreadyExists int = 6

	// PermissionDenied indicates the caller does not have permission to
	// execute the specified operation. It must not be used for rejections
	// caused by exhausting some resource (use ResourceExhausted
	// instead for those errors). It must not be
	// used if the caller cannot be identified (use Unauthenticated
	// instead for those errors).
	//
	// This error code will not be generated by the gRPC core framework,
	// but expect authentication middleware to use it.
	PermissionDenied int = 7

	// ResourceExhausted indicates some resource has been exhausted, perhaps
	// a per-user quota, or perhaps the entire file system is out of space.
	//
	// This error code will be generated by the gRPC framework in
	// out-of-memory and server overload situations, or when a message is
	// larger than the configured maximum size.
	ResourceExhausted int = 8

	// FailedPrecondition indicates operation was rejected because the
	// system is not in a state required for the operation's execution.
	// For example, directory to be deleted may be non-empty, an rmdir
	// operation is applied to a non-directory, etc.
	//
	// A litmus test that may help a service implementor in deciding
	// between FailedPrecondition, Aborted, and Unavailable:
	//  (a) Use Unavailable if the client can retry just the failing call.
	//  (b) Use Aborted if the client should retry at a higher-level
	//      (e.g., restarting a read-modify-write sequence).
	//  (c) Use FailedPrecondition if the client should not retry until
	//      the system state has been explicitly fixed. E.g., if an "rmdir"
	//      fails because the directory is non-empty, FailedPrecondition
	//      should be returned since the client should not retry unless
	//      they have first fixed up the directory by deleting files from it.
	//  (d) Use FailedPrecondition if the client performs conditional
	//      REST Get/Update/Delete on a resource and the resource on the
	//      server does not match the condition. E.g., conflicting
	//      read-modify-write on the same resource.
	//
	// This error code will not be generated by the gRPC framework.
	FailedPrecondition int = 9

	// Aborted indicates the operation was aborted, typically due to a
	// concurrency issue like sequencer check failures, transaction aborts,
	// etc.
	//
	// See litmus test above for deciding between FailedPrecondition,
	// Aborted, and Unavailable.
	//
	// This error code will not be generated by the gRPC framework.
	Aborted int = 10

	// OutOfRange means operation was attempted past the valid range.
	// E.g., seeking or reading past end of file.
	//
	// Unlike InvalidArgument, this error indicates a problem that may
	// be fixed if the system state changes. For example, a 32-bit file
	// system will generate InvalidArgument if asked to read at an
	// offset that is not in the range [0,2^32-1], but it will generate
	// OutOfRange if asked to read from an offset past the current
	// file size.
	//
	// There is a fair bit of overlap between FailedPrecondition and
	// OutOfRange. We recommend using OutOfRange (the more specific
	// error) when it applies so that callers who are iterating through
	// a space can easily look for an OutOfRange error to detect when
	// they are done.
	//
	// This error code will not be generated by the gRPC framework.
	OutOfRange int = 11

	// Unimplemented indicates operation is not implemented or not
	// supported/enabled in this service.
	//
	// This error code will be generated by the gRPC framework. Most
	// commonly, you will see this error code when a method implementation
	// is missing on the server. It can also be generated for unknown
	// compression algorithms or a disagreement as to whether an RPC should
	// be streaming.
	Unimplemented int = 12

	// Internal errors. Means some invariants expected by underlying
	// system has been broken. If you see one of these errors,
	// something is very broken.
	//
	// This error code will be generated by the gRPC framework in several
	// internal error conditions.
	Internal int = 13

	// Unavailable indicates the service is currently unavailable.
	// This is a most likely a transient condition and may be corrected
	// by retrying with a backoff. Note that it is not always safe to retry
	// non-idempotent operations.
	//
	// See litmus test above for deciding between FailedPrecondition,
	// Aborted, and Unavailable.
	//
	// This error code will be generated by the gRPC framework during
	// abrupt shutdown of a server process or network connection.
	Unavailable int = 14

	// DataLoss indicates unrecoverable data loss or corruption.
	//
	// This error code will not be generated by the gRPC framework.
	DataLoss int = 15

	// Unauthenticated indicates the request does not have valid
	// authentication credentials for the operation.
	//
	// The gRPC framework will generate this error code when the
	// authentication metadata is invalid or a Credentials callback fails,
	// but also expect authentication middleware to generate it.
	Unauthenticated int = 16

	MaxCode int = 17
)

var (
	DataAlreadyExists           = runtime.NewError("Entity already exists", (AlreadyExists))
	ErrorLoadingData            = runtime.NewError("Could not load data.", (NotFound))
	ErrorResolvingManifestMap   = runtime.NewError("Unable to load main Manifest", (NotFound))
	InvalidPayload              = runtime.NewError("Invalid payload.", (InvalidArgument))
	SectionNotFound             = runtime.NewError("Section not found.", (NotFound))
	BundleNotFound              = runtime.NewError("Bundle not found.", (NotFound))
	ItemNotFoundInBundle        = runtime.NewError("Item not found in Bundle.", (NotFound))
	MismatchedUser              = runtime.NewError("Invalid UserId", (PermissionDenied))
	InsufficientFunds           = runtime.NewError("User does not have enough funds to complete this Purchase.", (FailedPrecondition))
	MismatchedUserWalletBalance = runtime.NewError("User wallet balance mismatch during update.", (Aborted))
	MissingPayment              = runtime.NewError("Missing Payment amounts for Bundle", (FailedPrecondition))
	PaymentAmountInsufficient   = runtime.NewError("User payment amount insufficient to complete Purchase.", (FailedPrecondition))
	PaymentAmountExceedsCost    = runtime.NewError("User payment amount exceeds current cost.", (FailedPrecondition))
	MissingPrerequisites        = runtime.NewError("Missing Prerequisites for Bundle", (FailedPrecondition))
	NoUserIdFound               = runtime.NewError("No UserId found", (NotFound))
	RPCDeprecated               = runtime.NewError("This RPC/API Endpoint has been deprecated.", (NotFound))
	MissingVivoxConfig          = runtime.NewError("Missing Vivox config file", (Internal))
	MissingTokenConfig          = runtime.NewError("Missing Token config file", (Internal))
	NoTargetIdFound             = runtime.NewError("No TargetId found", (InvalidArgument))
	AccountNotFound             = runtime.NewError("Account not found", (InvalidArgument))
	RPCFailedAction             = runtime.NewError("This RPC/API Endpoint failed request.", (Internal))
	PartyNotFound               = runtime.NewError("Party not found", (InvalidArgument))
	TokenGenerationFailed       = runtime.NewError("Token generation failed", (InvalidArgument))
	PayloadMarshalFailed        = runtime.NewError("Payload marshal failed", (Internal))
	UserUnauthorized            = runtime.NewError("Unauthorized", (PermissionDenied))
	InformationNotFound         = runtime.NewError("Information not found", (InvalidArgument))
)

func ItemNotFound(itemId string) *runtime.Error {
	msg := fmt.Sprintf("Item not found %s", itemId)
	return runtime.NewError(msg, (NotFound))
}

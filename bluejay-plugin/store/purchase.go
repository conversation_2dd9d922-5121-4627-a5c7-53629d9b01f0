//purchase.go
package store

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/configs"
	"wildlight.gg/bluejay/entitlements"
	"wildlight.gg/bluejay/errors"
)

func GetAllStoreSections(ctx context.Context, _ runtime.Logger, _ *sql.DB, _ runtime.NakamaModule, _ string) (string, error) {
	storeSections, err := loadStoreSections(ctx)

	if err != nil {
		return "", err
	}
	
	storeSectionInfo, err := json.Marshal(storeSections)

	return string(storeSectionInfo), err
}

func GetStoreSectionsByIds(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

	request := entitlements.SectionIDsRequest{}

	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
		return "", err
	}

	if sections, err := loadSectionsByIdsInfo(ctx, request.IDs); err != nil {
		return "", err
	} else {
		sectionInfo, err := json.Marshal(SectionsListResponse{Sections: sections})

		return string(sectionInfo), err
	}

}

func GetAllBundleIds(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {

	if allBundles, err := loadBundles(ctx); err != nil {
		return "", err
	} else {
		bundleIds := []string{}
		for bundleId := range allBundles {
			bundleIds = append(bundleIds, bundleId)
		}

		payloadWrapper := entitlements.UserEntitlementResponse{IDs: bundleIds}
		bundleIdsInfo, err := json.Marshal(payloadWrapper)

		return string(bundleIdsInfo), err
	}

}

func GetBundlesByIds(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

	request := entitlements.BundleIDsRequest{}

	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
		return "", err
	}

	if allBundles, err := loadBundles(ctx); err != nil {
		return "", err
	} else {
		bundles := []BundleInfo{}
		for _, id := range request.IDs {

			if bundleInfo, ok := allBundles[id]; !ok {
				return "", errors.BundleNotFound
			} else {

				bundles = append(bundles, bundleInfo)

			}
		}

		bundlesResponse, err := json.Marshal(BundleListResponse{Bundles: bundles})

		return string(bundlesResponse), err
	}

}

func GetAllItemIds(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {

	if allItems, err := loadItems(ctx); err != nil {
		return "", err
	} else {
		ItemIds := []string{}
		for ItemId := range allItems {
			ItemIds = append(ItemIds, ItemId)
		}

		payloadWrapper := entitlements.UserEntitlementResponse{IDs: ItemIds}
		ItemIdsInfo, err := json.Marshal(payloadWrapper)

		return string(ItemIdsInfo), err
	}
}

func GetItemsByIds(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

	request := entitlements.ItemIDsRequest{}

	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
		return "", err
	}

	if allItems, err := loadItems(ctx); err != nil {
		return "", err
	} else {
		items := []configs.ItemInfo{}
		for _, id := range request.IDs {
			if info, ok := allItems[id]; !ok {
				return "", errors.ItemNotFound(id)
			} else {
				items = append(items, info)
			}
		}

		itemsList, err := json.Marshal(ItemsListResponse{Items: items})

		return string(itemsList), err
	}

}

func GetAllTreasureTroveIds(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {

	if allTreasureTroves, err := loadTreasureTroves(ctx); err != nil {
		return "", err
	} else {
		TreasureTroveIds := []string{}
		for TreasureTroveId := range allTreasureTroves {
			TreasureTroveIds = append(TreasureTroveIds, TreasureTroveId)
		}

		payloadWrapper := entitlements.UserEntitlementResponse{IDs: TreasureTroveIds}
		TreasureTroveIdInfo, err := json.Marshal(payloadWrapper)

		return string(TreasureTroveIdInfo), err
	}

}

func GetTreasureTrovesByIds(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

	request := entitlements.TreasureTroveIDsRequest{}

	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
		return "", err
	}

	if allTreasureTroves, err := loadTreasureTroves(ctx); err != nil {
		return "", err
	} else {

		filteredTreasureTroves := map[string]configs.TreasureTroveDefinition{}

		for _, troveId := range request.IDs {
			troveDefinition, exists := allTreasureTroves[troveId]
			if !exists {
				return "", errors.ItemNotFound(fmt.Sprintf("Treasure trove for ID %s not found.", troveId))
			}

			filteredTreasureTroves[troveId] = troveDefinition
		}

		troveDefMap, err := json.Marshal(TreasureTroveDefinitionResponse{TreasureTroves: filteredTreasureTroves})

		return string(troveDefMap), err
	}
}

func GetAllTrovePageIds(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {

	if allTrovePages, err := loadTrovesPages(ctx); err != nil {
		return "", err
	} else {
		TrovePageIds := []string{}
		for TrovePageId := range allTrovePages {
			TrovePageIds = append(TrovePageIds, TrovePageId)
		}

		payloadWrapper := entitlements.UserEntitlementResponse{IDs: TrovePageIds}
		TrovePageIdInfo, err := json.Marshal(payloadWrapper)

		return string(TrovePageIdInfo), err
	}

}

func GetTrovePagesByIds(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

	request := entitlements.TrovePageIDsRequest{}

	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
		return "", err
	}

	if allTrovePages, err := loadTrovesPages(ctx); err != nil {
		return "", err
	} else {

		filteredTrovePages := map[string]configs.TrovePageDefinition{}

		for _, trovePageId := range request.IDs {
			trovePageDef, exists := allTrovePages[trovePageId]

			if !exists {
				return "", errors.ItemNotFound(fmt.Sprintf("Trove page for id %s not found.", trovePageId))
			}

			filteredTrovePages[trovePageId] = trovePageDef
		}

		trovePagesMap, err := json.Marshal(TrovePageDefinitionResponse{TrovePages: filteredTrovePages})

		return string(trovePagesMap), err
	}

}

func GetCurrencyLimits(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

	if currencyLimits, err := loadCurrencyLimits(ctx); err != nil {
		return "", err
	} else {
		currencyLimitInfo, err := json.Marshal(currencyLimits)

		return string(currencyLimitInfo), err
	}
}

func PurchaseSingleItem(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if userID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		// User id must exist in the context for this RPC
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := PurchaseSingleItemPayload{}

		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", err
		}
		if itemMap, err := loadItems(ctx); err != nil {
			logger.Error("Error calling loadItems", err)
			return "", err
		} else
		// Verify items in users request also exist
		if itemInfo, ok := itemMap[request.ItemID]; !ok {
			return "", errors.ItemNotFound(request.ItemID)
		} else {
			if err := ensureItemMeetsCriteria(ctx, request.ItemID, request.PaymentAmount, request.PaymentCurrency, itemInfo.PricingInfo, logger, nk, userID); err != nil {
				return "", err
			} else {
				return processUpdateForSingleItem(ctx, nk, userID, request.PaymentCurrency, request.PaymentAmount, request.ItemID, logger)
			}

		}
	}
}

func PurchaseTreasureTrove(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if userID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		// User id must exist in the context for this RPC
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := PurchaseSingleItemPayload{}

		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", err
		}
		if treasureTroveMap, err := loadTreasureTroves(ctx); err != nil {
			logger.Error("Error calling loadTreasureTroves", err)
			return "", err
		} else
		// Verify items in users request also exist
		if treasureTroveInfo, ok := treasureTroveMap[request.ItemID]; !ok {
			return "", errors.ItemNotFound(request.ItemID)
		} else {
			if err := ensureItemMeetsCriteria(ctx, request.ItemID, request.PaymentAmount, request.PaymentCurrency, treasureTroveInfo.PricingInfo, logger, nk, userID); err != nil {
				return "", err
			} else {

				troveEntitlements := []string{treasureTroveInfo.TroveEntitlement, treasureTroveInfo.TroveInitialPage}

				return processUpdateForMultipleItems(ctx, nk, userID, request.PaymentCurrency, request.PaymentAmount, treasureTroveInfo.TroveEntitlement, troveEntitlements, logger)
			}

		}
	}
}

func PurchaseTrovePageItem(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if userID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		// User id must exist in the context for this RPC
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := PurchaseSingleTroveItemPayload{}

		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", err
		}

		hasTreasureTrovePage, err := entitlements.HasEntitlements(ctx, nk, userID, []string{request.TrovePageId}, logger)

		if err != nil {
			return "", err
		}

		if !hasTreasureTrovePage {
			return "", errors.ItemNotFound(fmt.Sprintf("Player does not have Treasure Trove page %s entitlement.", request.TrovePageId))
		}

		if treasureTrovePagesMap, err := loadTrovesPages(ctx); err != nil {
			logger.Error("Error calling loadTreasureTroves", err)
			return "", err
		} else
		// Verify items in users request also exist
		if treasureTrovePageInfo, ok := treasureTrovePagesMap[request.TrovePageId]; !ok {
			return "", errors.ItemNotFound(request.ItemID)
		} else {

			if itemMap, err := loadItems(ctx); err != nil {
				logger.Error("Error calling loadItems", err)
				return "", err
			} else
			// Verify items in users request also exist
			if itemInfo, ok := itemMap[request.ItemID]; !ok {
				return "", errors.ItemNotFound(request.ItemID)
			} else {

				if err := ensureItemMeetsCriteria(ctx, request.ItemID, request.PaymentAmount, request.PaymentCurrency, itemInfo.PricingInfo, logger, nk, userID); err != nil {
					return "", err
				} else {
					nextPage, ok := treasureTrovePagesMap[treasureTrovePageInfo.NextPage]
					var nextPageId string = ""
					var threshold int64 = 0
					if ok {
						nextPageId = treasureTrovePageInfo.NextPage
						threshold = nextPage.PricingInfo.FullPrice
					}

					return processUpdateForSingleTroveItem(ctx, nk, userID, request.PaymentCurrency, request.PaymentAmount, request.ItemID, request.TrovePageId, nextPageId, threshold, logger)
				}
			}

		}
	}
}

func PurchaseBundle(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if userID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		// User id must exist in the context for this RPC
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := PurchaseBundlePayload{}

		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", err
		}

		// Load bundle(s) information
		{
			if bundleInfo, err := ensureBundleMeetsCriteria(ctx, request.BundleId, request.PaymentAmount, logger, nk, userID); err != nil {
				return "", err
			} else {
				itemsFromBundleToPurhcase := []string{}

				// We may be "regranting" items the user already owns
				for itemId := range bundleInfo.Items {
					itemsFromBundleToPurhcase = append(itemsFromBundleToPurhcase, itemId)
				}

				return processUpdateForMultipleItems(ctx, nk, userID, request.PaymentCurrency, request.PaymentAmount, bundleInfo.Id, itemsFromBundleToPurhcase, logger)
			}
		}
	}
}

func GetUserWalletInfo(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if userID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {

		request := &entitlements.GetUserInfoPayload{}

		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", errors.InvalidPayload
		}

		if account, err := nk.AccountGetId(ctx, userID); err != nil {
			return "", err
		} else {
			return account.Wallet, nil
		}
	}
}

func GetUserWalletLedgerInfo(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if userID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := &entitlements.GetUserInfoPayload{}

		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", errors.InvalidPayload
		}

		if ledgerItems, cursor, err := nk.WalletLedgerList(ctx, userID, 10, request.Cursor); err != nil {
			return "", err
		} else {
			walletString, err := json.Marshal(TransactionList{Transactions: ledgerItems, Cursor: cursor})

			return string(walletString), err
		}
	}
}

func GrantCurrency(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if userID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := &entitlements.GrantCurrencyPayload{}

		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", err
		}

		logger.Debug("grantCurrency request object %s", (request))

		if account, err := nk.AccountGetId(ctx, userID); err != nil {
			return "", err
		} else {

			userWallet := map[string]int64{}

			if err := json.Unmarshal([]byte(account.Wallet), &userWallet); err != nil {
				logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
				return "", err
			}

			grants := map[string]int64{request.Currency: request.Delta}

			if updated, previous, err := nk.WalletUpdate(ctx, userID, grants, nil, true); err != nil {
				return "", err
			} else {

				updatedResponse := runtime.WalletUpdateResult{UserID: userID, Updated: updated, Previous: previous}
				logger.Debug("updatedResponse object %s", (updatedResponse))
				if response, err := json.Marshal(updatedResponse); err != nil {
					logger.Error("Error creating JSON string", err)
					return "", err
				} else {
					return string(response), nil
				}

			}
		}

	}
}

func ResetCurrency(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {
	if userID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {

		if account, err := nk.AccountGetId(ctx, userID); err != nil {
			return "", err
		} else {
			wallet := map[string]int64{}
			changeset := map[string]int64{}

			if err := json.Unmarshal([]byte(account.Wallet), &wallet); err != nil {
				return "", err
			}

			for currency, value := range wallet {
				changeset[currency] = -value
			}

			if updated, previous, err := nk.WalletUpdate(ctx, userID, changeset, map[string]interface{}{"reason": "Reset Currency"}, true); err != nil {
				return "", nil
			} else {
				walletString, err := json.Marshal(runtime.WalletUpdateResult{UserID: userID, Updated: updated, Previous: previous})

				return string(walletString), err
			}
		}
	}
}

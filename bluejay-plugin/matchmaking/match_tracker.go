package matchmaking

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/storage"
	"wildlight.gg/bluejay/utils"
)

type playerStandingRecord struct {
	Standing           int   `json:"standing"`
	StandingResetTime  int64 `json:"standingResetTime,omitempty"`
	CooldownExpireTime int64 `json:"cooldownExpireTime,omitempty"`
	ActiveMatch        struct {
		InstanceId string `json:"instanceId,omitempty"`
		ServerIp   string `json:"serverIp,omitempty"`
		ServerPort int    `json:"serverPort,omitempty"`
		StartTime  int64  `json:"startTime,omitempty"`
	} `json:"activeMatch"`
}

type onMatchCompleteRequest struct {
	InstanceId  string   `json:"instanceId"`
	Environment string   `json:"environment"`
	PlayerIds   []string `json:"playerIds"`
}

type MatchTracker struct {
	nakamaModule runtime.NakamaModule
}

func NewMatchTracker(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, initializer runtime.Initializer) (*MatchTracker, error) {
	matchTracker := &MatchTracker{
		nakamaModule: nk,
	}

	err := utils.RegisterWildlightRpc(initializer, logger, "onMatchComplete", matchTracker.OnMatchComplete)
	if err != nil {
		logger.Error("Error registering submitPlayerRankings rpc: %v", err)
		return nil, err
	}

	return matchTracker, nil
}

func (mt *MatchTracker) OnMatchComplete(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var request onMatchCompleteRequest

	err := json.Unmarshal([]byte(payload), &request)
	if err != nil {
		logger.WithField("err", err).Error("Error parsing OnMatchComplete payload")
		return "", err
	}

	err = RemovePlayersFromMatch(ctx, logger, nk, request.Environment, request.InstanceId, request.PlayerIds)
	if err != nil {
		logger.WithField("err", err).Error("error processing OnMatchComplete")
		return "", err
	}

	return "", nil
}

func (mt *MatchTracker) OnSessionStart(ctx context.Context, logger runtime.Logger, playerId string, environment string) error {
	logger = logger.WithField("playerId", playerId).WithField("env", environment)

	standings, err := readPlayerStanding(ctx, logger, mt.nakamaModule, environment, []string{playerId})
	if err != nil {
		logger.WithField("err", err).Error("error reading player reconnect state")
		return err
	}

	standing, found := standings[playerId]
	if found {
		if fixupResetTimes(logger, &standing) {
			standings[playerId] = standing
			_ = updatePlayerStanding(ctx, logger, mt.nakamaModule, environment, standings)
		}
	} else {
		standing = playerStandingRecord{}
	}

	msg := map[string]interface{}{
		"reconnect": standing,
	}

	mt.nakamaModule.NotificationSend(ctx, playerId, "server", msg, utils.RECONNECT_INFO, utils.SystemUserId, false)

	return nil
}

func newPlayerStandingRecord() playerStandingRecord {
	return playerStandingRecord{}
}

func readPlayerStanding(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, environment string, playerIds []string) (map[string]playerStandingRecord, error) {
	return storage.ReadPlayerStorageObjects(ctx, logger, nk, environment, storage.MATCHMAKING_COLLECTION, storage.USER_STANDING, playerIds, newPlayerStandingRecord)
}

func updatePlayerStanding(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, environment string, newStanding map[string]playerStandingRecord) error {
	return storage.UpdatePlayerStorageObjects(ctx, logger, nk, environment, storage.MATCHMAKING_COLLECTION, storage.USER_STANDING, newStanding)
}

func fixupResetTimes(logger runtime.Logger, standingRecord *playerStandingRecord) bool {
	now := time.Now().Unix()
	dirty := false

	if standingRecord.StandingResetTime > 0 && standingRecord.StandingResetTime < now {
		standingRecord.Standing = 0
		standingRecord.StandingResetTime = 0
		standingRecord.CooldownExpireTime = 0

		logger.Info("Player matchmaking standing reset to 0")
		dirty = true
	}
	if standingRecord.CooldownExpireTime > 0 && standingRecord.CooldownExpireTime < now {
		standingRecord.CooldownExpireTime = 0
		logger.Info("Player matchmaking cooldown reset to 0")
		dirty = true
	}

	return dirty
}

func AddPlayersToMatch(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, environment string, instanceId string, serverIp string, serverPort int, playerIds []string) error {
	logger = logger.WithField("instanceId", instanceId).WithField("env", environment)

	standings, err := readPlayerStanding(ctx, logger, nk, environment, playerIds)
	if err != nil {
		logger.WithField("err", err).Error("error adding players to match")
		return err
	}

	now := time.Now().Unix()
	for playerId, standingRecord := range standings {
		playerLogger := logger.WithField("playerId", playerId)
		fixupResetTimes(playerLogger, &standingRecord)

		standingRecord.ActiveMatch.InstanceId = instanceId
		standingRecord.ActiveMatch.ServerIp = serverIp
		standingRecord.ActiveMatch.ServerPort = serverPort
		standingRecord.ActiveMatch.StartTime = now
		playerLogger.Info("Setting player active match")

		standings[playerId] = standingRecord

		msg := map[string]interface{}{
			"reconnect": standingRecord,
		}

		nk.NotificationSend(ctx, playerId, "server", msg, utils.RECONNECT_INFO, utils.SystemUserId, false)
	}

	err = updatePlayerStanding(ctx, logger, nk, environment, standings)
	if err != nil {
		logger.WithField("err", err).Error("error adding players to match")
		return err
	}

	return nil
}

func RemovePlayersFromMatch(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, environment string, instanceId string, playerIds []string) error {
	logger = logger.WithField("instanceId", instanceId).WithField("env", environment)

	standings, err := readPlayerStanding(ctx, logger, nk, environment, playerIds)
	if err != nil {
		logger.WithField("err", err).Error("error removing players from match")
		return err
	}

	for playerId, standingRecord := range standings {
		playerLogger := logger.WithField("playerId", playerId)
		fixupResetTimes(playerLogger, &standingRecord)

		if standingRecord.ActiveMatch.InstanceId == instanceId {
			standingRecord.ActiveMatch.InstanceId = ""
			standingRecord.ActiveMatch.ServerIp = ""
			standingRecord.ActiveMatch.ServerPort = 0
			standingRecord.ActiveMatch.StartTime = 0
			playerLogger.Info("Clearing player active match")

			msg := map[string]interface{}{
				"reconnect": standingRecord,
			}

			nk.NotificationSend(ctx, playerId, "server", msg, utils.RECONNECT_INFO, utils.SystemUserId, false)
		}

		standings[playerId] = standingRecord
	}

	err = updatePlayerStanding(ctx, logger, nk, environment, standings)
	if err != nil {
		logger.WithField("err", err).Error("error removing players from match")
		return err
	}

	return nil
}

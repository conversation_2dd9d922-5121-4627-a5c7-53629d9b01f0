package matchmaking

import (
	"context"

	"github.com/heroiclabs/nakama-common/runtime"
	"github.com/intinig/go-openskill/types"
	"wildlight.gg/bluejay/playlists"
	"wildlight.gg/bluejay/storage"
)

type matchmakerPlayer struct {
	Id     string
	Rating types.Rating
	Entry  runtime.MatchmakerEntry
}

type skillRecord struct {
	Mu    float64
	Sigma float64
}

type playerSkillRecord struct {
	Buckets map[string]skillRecord
}

func newPlayerSkillRecord() playerSkillRecord {
	return playerSkillRecord{
		Buckets: map[string]skillRecord{},
	}
}

func readSkill(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, environment string, playerIds []string) (map[string]playerSkillRecord, error) {
	return storage.ReadPlayerStorageObjects(ctx, logger, nk, environment, storage.MATCHMAKING_COLLECTION, storage.USER_SKILL, playerIds, newPlayerSkillRecord)
}

func getDefaultSkill(skillBucket *playlists.SkillBucket) skillRecord {
	return skillRecord{
		Mu:    skillBucket.DefaultMu,
		Sigma: skillBucket.DefaultSigma,
	}
}

func getSkillForBucket(skillBucket *playlists.SkillBucket, playerSkillRecord playerSkillRecord) skillRecord {
	bucket, found := playerSkillRecord.Buckets[skillBucket.Name]
	if found {
		return bucket
	}
	return getDefaultSkill(skillBucket)
}

func updateSkill(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, environment string, players map[string]playerSkillRecord) error {
	return storage.UpdatePlayerStorageObjects(ctx, logger, nk, environment, storage.MATCHMAKING_COLLECTION, storage.USER_SKILL, players)
}
